import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import styled from 'styled-components';
const HeaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.lg || '24px'};
  margin-bottom: ${({ theme }) => theme.spacing?.xl || '32px'};
`;
const F1Header = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: ${({ theme }) => theme.spacing?.lg || '24px'};
  background: linear-gradient(
    135deg,
    ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'} 0%,
    rgba(75, 85, 99, 0.1) 100%
  );
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.lg || '8px'};
  position: relative;
  overflow: hidden;

  /* F1 Racing accent line */
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(
      90deg,
      ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'} 0%,
      transparent 100%
    );
  }
`;
const F1Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes?.h2 || '1.5rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 2px;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;

  span {
    color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    font-weight: 800;
  }
`;
const GuideIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  color: ${({ $hasDate, theme }) =>
    $hasDate
      ? theme.colors?.success || 'var(--success-color)'
      : theme.colors?.textSecondary || 'var(--text-secondary)'};
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 1px;
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};
  border: 1px solid
    ${({ $hasDate, theme }) =>
      $hasDate
        ? theme.colors?.success || 'var(--success-color)'
        : theme.colors?.border || 'var(--border-primary)'};
  background: ${({ $hasDate, theme }) =>
    $hasDate ? `${theme.colors?.success || 'var(--success-color)'}20` : 'transparent'};

  &::before {
    content: '📅';
    font-size: 12px;
  }
`;
const SubHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: ${({ theme }) => theme.spacing?.md || '12px'};
  border-bottom: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
`;
const TitleSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const SubTitle = styled.h2`
  font-size: ${({ theme }) => theme.fontSizes?.xxl || '1.875rem'};
  margin: 0;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-weight: 600;
`;
const DateBadge = styled.span`
  background: ${({ theme }) => theme.colors?.surface || 'var(--bg-secondary)'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  padding: ${({ theme }) => theme.spacing?.xxs || '2px'}
    ${({ theme }) => theme.spacing?.sm || '8px'};
  border-radius: ${({ theme }) => theme.borderRadius?.full || '9999px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  font-weight: 600;
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;
const ActionsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const DateSelector = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.sm || '8px'};
`;
const DateInput = styled.input`
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  background: ${({ theme }) => theme.colors?.background || 'var(--bg-primary)'};
  border: 1px solid ${({ theme }) => theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;

  &:focus {
    outline: none;
    border-color: ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'};
    box-shadow: 0 0 0 3px ${({ theme }) => theme.colors?.primary || 'var(--primary-color)'}20;
    transform: translateY(-1px);
  }

  &::-webkit-calendar-picker-indicator {
    filter: invert(1);
    cursor: pointer;
  }
`;
const ActionButton = styled.button`
  background: ${({ $variant, theme }) =>
    $variant === 'primary' ? theme.colors?.primary || 'var(--primary-color)' : 'transparent'};
  color: ${({ $variant, theme }) =>
    $variant === 'primary'
      ? theme.colors?.textInverse || '#ffffff'
      : theme.colors?.textSecondary || 'var(--text-secondary)'};
  border: 1px solid
    ${({ $variant, theme }) =>
      $variant === 'primary'
        ? theme.colors?.primary || 'var(--primary-color)'
        : theme.colors?.border || 'var(--border-primary)'};
  border-radius: ${({ theme }) => theme.borderRadius?.md || '6px'};
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  min-width: 100px;
  justify-content: center;

  &:hover:not(:disabled) {
    background: ${({ $variant, theme }) =>
      $variant === 'primary'
        ? theme.colors?.primaryDark || 'var(--primary-dark)'
        : theme.colors?.surface || 'var(--bg-secondary)'};
    transform: translateY(-1px);
    box-shadow: 0 4px 8px
      ${({ $variant, theme }) =>
        $variant === 'primary'
          ? `${theme.colors?.primary || 'var(--primary-color)'}40`
          : 'rgba(0, 0, 0, 0.1)'};
  }

  &:active:not(:disabled) {
    transform: translateY(0);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;
/**
 * F1GuideHeader Component
 *
 * PATTERN: F1 Header Pattern
 * - Racing-inspired styling with date selector
 * - Current date indicator and navigation
 * - Consistent with F1 design system
 * - Accessible and responsive
 * - Professional trading guide appearance
 */
export const F1GuideHeader = ({
  className,
  isLoading = false,
  isRefreshing = false,
  currentDate,
  selectedDate,
  onDateChange,
  onRefresh,
  title = 'Daily Trading Guide',
}) => {
  const hasDate = !!currentDate;
  const maxDate = new Date().toISOString().split('T')[0];
  const handleDateChange = e => {
    if (onDateChange) {
      onDateChange(e.target.value);
    }
  };
  return _jsxs(HeaderContainer, {
    className: className,
    children: [
      _jsxs(F1Header, {
        children: [
          _jsxs(F1Title, {
            children: ['\uD83C\uDFCE\uFE0F DAILY ', _jsx('span', { children: 'GUIDE' })],
          }),
          _jsx(GuideIndicator, { $hasDate: hasDate, children: hasDate ? currentDate : 'NO DATE' }),
        ],
      }),
      _jsxs(SubHeader, {
        children: [
          _jsxs(TitleSection, {
            children: [
              _jsx(SubTitle, { children: title }),
              hasDate && _jsxs(DateBadge, { children: ['\uD83D\uDCC5 ', currentDate] }),
            ],
          }),
          _jsxs(ActionsContainer, {
            children: [
              _jsx(DateSelector, {
                children: _jsx(DateInput, {
                  type: 'date',
                  value: selectedDate || '',
                  onChange: handleDateChange,
                  max: maxDate,
                  title: 'Select date for trading guide',
                }),
              }),
              onRefresh &&
                _jsxs(ActionButton, {
                  $variant: 'primary',
                  onClick: onRefresh,
                  disabled: isLoading,
                  title: isLoading ? 'Refreshing guide...' : 'Refresh guide data',
                  children: [
                    isLoading || isRefreshing ? '⏳' : '🔄',
                    isLoading ? 'Refreshing' : 'Refresh',
                  ],
                }),
            ],
          }),
        ],
      }),
    ],
  });
};
export default F1GuideHeader;
//# sourceMappingURL=F1GuideHeader.js.map
