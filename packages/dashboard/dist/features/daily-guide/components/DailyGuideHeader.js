import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import styled from 'styled-components';
import { Button } from '@adhd-trading-dashboard/shared';
const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${({ theme }) => theme.spacing?.lg || '16px'} 0;
  border-bottom: 2px solid var(--border-primary);
  margin-bottom: ${({ theme }) => theme.spacing?.lg || '16px'};
`;
const TitleSection = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
`;
const Title = styled.h1`
  font-size: ${({ theme }) => theme.fontSizes?.xxl || '2rem'};
  font-weight: 700;
  color: ${({ theme }) => theme.colors?.textPrimary || '#ffffff'};
  margin: 0;
  letter-spacing: -0.025em;

  /* F1 Racing aesthetic */
  text-transform: uppercase;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
`;
const Subtitle = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.sm || '0.875rem'};
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;
const ActionsSection = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.md || '12px'};
`;
const DateDisplay = styled.div`
  font-size: ${({ theme }) => theme.fontSizes?.md || '1rem'};
  color: ${({ theme }) => theme.colors?.textSecondary || 'var(--text-secondary)'};
  font-weight: 500;
  padding: ${({ theme }) => theme.spacing?.sm || '8px'}
    ${({ theme }) => theme.spacing?.md || '12px'};
  background: rgba(156, 163, 175, 0.1);
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  border: 1px solid var(--border-primary);
`;
const RefreshButton = styled(Button)`
  min-width: 100px;
  position: relative;

  ${({ isRefreshing }) =>
    isRefreshing &&
    `
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 16px;
      height: 16px;
      margin: -8px 0 0 -8px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  `}
`;
const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: ${({ theme }) => theme.spacing?.xs || '4px'};
  padding: ${({ theme }) => theme.spacing?.xs || '4px'} ${({ theme }) => theme.spacing?.sm || '8px'};
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid var(--success-color);
  border-radius: ${({ theme }) => theme.borderRadius?.sm || '4px'};
  font-size: ${({ theme }) => theme.fontSizes?.xs || '0.75rem'};
  font-weight: 600;
  color: var(--success-color);
  text-transform: uppercase;
  letter-spacing: 0.05em;
`;
const StatusDot = styled.div`
  width: 6px;
  height: 6px;
  background: var(--success-color);
  border-radius: 50%;
  animation: pulse 2s infinite;

  @keyframes pulse {
    0%,
    100% {
      opacity: 1;
    }
    50% {
      opacity: 0.5;
    }
  }
`;
/**
 * DailyGuideHeader Component
 *
 * F1-themed header that provides consistent branding and actions
 * for the Daily Guide feature.
 */
export const DailyGuideHeader = ({ currentDate, onRefresh, isRefreshing = false, className }) => {
  return _jsxs(HeaderContainer, {
    className: className,
    children: [
      _jsxs(TitleSection, {
        children: [
          _jsx(Title, { children: 'Daily Trading Guide' }),
          _jsx(Subtitle, { children: 'Market Insights & Strategy' }),
        ],
      }),
      _jsxs(ActionsSection, {
        children: [
          _jsxs(StatusIndicator, { children: [_jsx(StatusDot, {}), 'LIVE GUIDE'] }),
          currentDate && _jsx(DateDisplay, { children: currentDate }),
          onRefresh &&
            _jsx(RefreshButton, {
              variant: 'outline',
              size: 'small',
              onClick: onRefresh,
              disabled: isRefreshing,
              isRefreshing: isRefreshing,
              children: isRefreshing ? 'Refreshing...' : 'Refresh',
            }),
        ],
      }),
    ],
  });
};
export default DailyGuideHeader;
//# sourceMappingURL=DailyGuideHeader.js.map
