import { jsx as _jsx, jsxs as _jsxs } from 'react/jsx-runtime';
import styled from 'styled-components';
// Styled components
const IndexGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: ${({ theme }) => theme.spacing.md};
  margin-top: ${({ theme }) => theme.spacing.md};
`;
const IndexCard = styled.div`
  padding: ${({ theme }) => theme.spacing.md};
  background-color: ${({ theme }) => theme.colors.background};
  border-radius: ${({ theme }) => theme.borderRadius.sm};
  display: flex;
  flex-direction: column;
  border: 1px solid var(--border-primary);
  transition: all 0.2s ease;

  &:hover {
    border-color: ${({ theme }) => theme.colors.primary};
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
`;
const IndexName = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: var(--text-secondary);
  margin-bottom: ${({ theme }) => theme.spacing.xs};
  font-weight: 500;
`;
const IndexValue = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.lg};
  font-weight: 600;
  color: ${({ theme }) => theme.colors.textPrimary};
  margin-bottom: ${({ theme }) => theme.spacing.xs};
`;
const IndexChange = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.sm};
  color: ${({ theme, value }) => (value >= 0 ? theme.colors.profit : theme.colors.loss)};
  display: flex;
  align-items: center;
  margin-top: ${({ theme }) => theme.spacing.xs};
  font-weight: 500;
`;
const PreviousClose = styled.div`
  font-size: ${({ theme }) => theme.fontSizes.xs};
  color: var(--text-secondary);
  margin-top: ${({ theme }) => theme.spacing.xs};
`;
const EmptyState = styled.div`
  text-align: center;
  padding: ${({ theme }) => theme.spacing.lg};
  color: ${({ theme }) => theme.colors.textSecondary};
  font-style: italic;
`;
/**
 * Market Indicators Component
 *
 * Displays market indices with their values, changes, and previous close data.
 */
export const MarketIndicators = ({ indices }) => {
  if (!indices || indices.length === 0) {
    return _jsx(EmptyState, { children: 'No market indices data available' });
  }
  return _jsx(IndexGrid, {
    children: indices.map(index =>
      _jsxs(
        IndexCard,
        {
          children: [
            _jsx(IndexName, { children: index.name }),
            _jsx(IndexValue, { children: index.value.toFixed(2) }),
            _jsxs(IndexChange, {
              value: index.change,
              children: [
                index.change >= 0 ? '▲ ' : '▼ ',
                Math.abs(index.change).toFixed(2),
                ' (',
                (index.changePercent * 100).toFixed(2),
                '%)',
              ],
            }),
            index.previousClose &&
              _jsxs(PreviousClose, { children: ['Previous: ', index.previousClose.toFixed(2)] }),
          ],
        },
        index.symbol
      )
    ),
  });
};
//# sourceMappingURL=MarketIndicators.js.map
