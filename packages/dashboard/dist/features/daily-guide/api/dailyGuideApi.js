/**
 * Daily Guide API
 *
 * API functions for the daily guide feature
 */
/**
 * Fetch daily guide data from the API
 *
 * In a real application, this would make an actual API call.
 * For now, we're generating mock data.
 */
export const fetchDailyGuideData = async () => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 800));
  // Randomly decide if we should throw an error (for testing error handling)
  const shouldError = Math.random() < 0.05; // 5% chance of error
  if (shouldError) {
    throw new Error('Failed to fetch daily guide data');
  }
  return generateMockData();
};
/**
 * Generate mock data for development and testing
 */
const generateMockData = () => {
  // Market data removed as it's not part of the DailyGuideData interface
  // Trading plan
  // Using different priorities for each item
  // const priorities: TradingPlanPriority[] = ['high', 'medium', 'low'];
  const tradingPlan = [
    {
      id: '1',
      description: 'Wait for market open before placing any trades',
      priority: 'high',
      completed: false,
    },
    {
      id: '2',
      description: 'Focus on tech sector for long opportunities',
      priority: 'medium',
      completed: false,
    },
    {
      id: '3',
      description: 'Use tight stop losses due to expected volatility',
      priority: 'high',
      completed: false,
    },
    {
      id: '4',
      description: 'Review earnings reports for potential opportunities',
      priority: 'medium',
      completed: false,
    },
    {
      id: '5',
      description: 'Avoid over-trading in the first hour',
      priority: 'low',
      completed: false,
    },
  ];
  // Key levels removed - not part of DailyGuideData interface
  // Market news
  const marketNews = [
    {
      id: '1',
      title: 'Fed signals potential rate hike in upcoming meeting',
      source: 'Financial Times',
      url: 'https://example.com/news/1',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      impact: 'high',
    },
    {
      id: '2',
      title: 'Tech stocks rally on strong earnings reports',
      source: 'Wall Street Journal',
      url: 'https://example.com/news/2',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
      impact: 'medium',
    },
    {
      id: '3',
      title: 'Oil prices stabilize after recent volatility',
      source: 'Bloomberg',
      url: 'https://example.com/news/3',
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
      impact: 'low',
    },
  ];
  return {
    tradingPlan: {
      items: tradingPlan,
      strategy: 'Focus on FVG and RD setups during high-volume sessions',
      riskManagement: {
        maxRiskPerTrade: 1.5,
        maxDailyLoss: 3.0,
        maxTrades: 5,
        positionSizing: '1% of account per trade',
      },
      notes: 'Market showing signs of consolidation. Be patient and wait for clear setups.',
    },
    marketNews,
    // Add missing required properties for DailyGuideData
    marketOverview: {
      sentiment: 'neutral',
      summary: 'Market showing mixed signals with moderate volatility',
      indices: [],
      economicEvents: [],
      news: [],
      lastUpdated: new Date().toISOString(),
    },
    keyPriceLevels: [],
    watchlist: [],
  };
};
//# sourceMappingURL=dailyGuideApi.js.map
